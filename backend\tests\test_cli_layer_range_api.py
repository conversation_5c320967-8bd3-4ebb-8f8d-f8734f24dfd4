"""
Tests for CLI Layer Range API endpoints
======================================

This module contains comprehensive tests for the new CLI layer range functionality
including endpoint tests and CLI parser service tests for multi-layer operations.
"""

import pytest
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient
from io import BytesIO

from app.main import app
from infrastructure.recoater_client import RecoaterConnectionError, RecoaterAPIError
from backend.infrastructure.cli_editor.editor import CliParsingError, ParsedCliFile, CliLayer, Point, Polyline, Hatch


@pytest.fixture
def client():
    """Create a test client for the FastAPI app."""
    return TestClient(app)


class TestCliLayerRangeEndpoint:
    """Test CLI layer range endpoint functionality."""

    @patch('app.api.print.Editor')
    def test_send_cli_layer_range_success(self, mock_parser_class, client, mock_recoater_client):
        """Test successful layer range sending with valid ranges."""
        # Setup mock parser
        mock_parser = Mock()
        mock_parser_class.return_value = mock_parser

        # Create mock layers with different z-heights
        mock_layer1 = CliLayer(z_height=0.1, polylines=[], hatches=[])
        mock_layer2 = CliLayer(z_height=0.2, polylines=[], hatches=[])
        mock_layer3 = CliLayer(z_height=0.3, polylines=[], hatches=[])
        mock_parsed_data = ParsedCliFile(
            header_lines=["$$UNITS/1", "$$HEADEREND"],
            is_aligned=False,
            layers=[mock_layer1, mock_layer2, mock_layer3]
        )
        mock_parser.parse.return_value = mock_parsed_data

        # Mock CLI generation for layer range
        mock_cli_data = b"mock multi-layer cli data"
        mock_parser.generate_ascii_cli_from_layer_range.return_value = mock_cli_data

        # Reset and configure the global mock for this specific test
        mock_recoater_client.reset_mock()
        mock_recoater_client.upload_drum_geometry.return_value = {
            "success": True,
            "drum_id": 1,
            "file_size": len(mock_cli_data)
        }

        # Upload CLI file first
        test_file_content = b"fake cli content"
        files = {"file": ("test.cli", BytesIO(test_file_content), "application/octet-stream")}
        upload_response = client.post("/api/v1/print/cli/upload", files=files)
        assert upload_response.status_code == 200
        file_id = upload_response.json()["file_id"]

        # Send layer range to drum
        layer_range_request = {
            "start_layer": 1,
            "end_layer": 2
        }
        response = client.post(
            f"/api/v1/print/cli/{file_id}/layers/send/1",
            json=layer_range_request
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["file_id"] == file_id
        assert data["drum_id"] == 1
        assert data["layer_range"] == [1, 2]
        assert data["z_height_range"] == [0.1, 0.2]
        assert data["data_size"] == len(mock_cli_data)
        assert "Layer range 1-2" in data["message"]

        # Verify CLI generation was called correctly
        mock_parser.generate_ascii_cli_from_layer_range.assert_called_once_with(
            layers=[mock_layer1, mock_layer2],
            header_lines=["$$UNITS/1", "$$HEADEREND"]
        )

        # Verify upload was called correctly
        mock_recoater_client.upload_drum_geometry.assert_called_once_with(
            drum_id=1,
            file_data=mock_cli_data,
            content_type="application/octet-stream"
        )

    @patch('app.api.print.Editor')
    def test_send_cli_layer_range_single_layer(self, mock_parser_class, client, mock_recoater_client):
        """Test sending a single layer using layer range endpoint."""
        # Setup mock parser
        mock_parser = Mock()
        mock_parser_class.return_value = mock_parser

        mock_layer = CliLayer(z_height=0.5, polylines=[], hatches=[])
        mock_parsed_data = ParsedCliFile(
            header_lines=["$$HEADEREND"],
            is_aligned=True,
            layers=[mock_layer, mock_layer, mock_layer]  # 3 identical layers
        )
        mock_parser.parse.return_value = mock_parsed_data

        # Mock CLI generation
        mock_cli_data = b"single layer cli data"
        mock_parser.generate_ascii_cli_from_layer_range.return_value = mock_cli_data

        # Mock recoater client
        mock_recoater_client.upload_drum_geometry.return_value = {
            "success": True,
            "drum_id": 2,
            "file_size": len(mock_cli_data)
        }

        # Upload CLI file
        files = {"file": ("test.cli", BytesIO(b"content"), "application/octet-stream")}
        upload_response = client.post("/api/v1/print/cli/upload", files=files)
        file_id = upload_response.json()["file_id"]

        # Send single layer (start_layer == end_layer)
        layer_range_request = {
            "start_layer": 2,
            "end_layer": 2
        }
        response = client.post(
            f"/api/v1/print/cli/{file_id}/layers/send/2",
            json=layer_range_request
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["layer_range"] == [2, 2]
        assert data["z_height_range"] == [0.5, 0.5]

        # Verify only one layer was passed
        mock_parser.generate_ascii_cli_from_layer_range.assert_called_once_with(
            layers=[mock_layer],  # Only the second layer (index 1)
            header_lines=["$$HEADEREND"]
        )

    def test_send_cli_layer_range_file_not_found(self, client):
        """Test layer range send with non-existent file ID."""
        layer_range_request = {
            "start_layer": 1,
            "end_layer": 3
        }
        response = client.post(
            "/api/v1/print/cli/nonexistent-id/layers/send/1",
            json=layer_range_request
        )

        assert response.status_code == 404
        assert "CLI file not found" in response.json()["detail"]

    @patch('app.api.print.Editor')
    def test_send_cli_layer_range_invalid_start_greater_than_end(self, mock_parser_class, client):
        """Test validation error when start_layer > end_layer."""
        # Setup and upload file
        mock_parser = Mock()
        mock_parser_class.return_value = mock_parser
        mock_parsed_data = ParsedCliFile(
            header_lines=["$$HEADEREND"],
            is_aligned=False,
            layers=[CliLayer(z_height=0.1, polylines=[], hatches=[])] * 5
        )
        mock_parser.parse.return_value = mock_parsed_data

        files = {"file": ("test.cli", BytesIO(b"content"), "application/octet-stream")}
        upload_response = client.post("/api/v1/print/cli/upload", files=files)
        file_id = upload_response.json()["file_id"]

        # Test invalid range (start > end)
        layer_range_request = {
            "start_layer": 4,
            "end_layer": 2
        }
        response = client.post(
            f"/api/v1/print/cli/{file_id}/layers/send/1",
            json=layer_range_request
        )

        assert response.status_code == 400
        assert "Start layer cannot be greater than end layer" in response.json()["detail"]

    @patch('app.api.print.Editor')
    def test_send_cli_layer_range_out_of_bounds(self, mock_parser_class, client):
        """Test validation errors for out of bounds layer numbers."""
        # Setup and upload file with 3 layers
        mock_parser = Mock()
        mock_parser_class.return_value = mock_parser
        mock_parsed_data = ParsedCliFile(
            header_lines=["$$HEADEREND"],
            is_aligned=False,
            layers=[CliLayer(z_height=i*0.1, polylines=[], hatches=[]) for i in range(1, 4)]
        )
        mock_parser.parse.return_value = mock_parsed_data

        files = {"file": ("test.cli", BytesIO(b"content"), "application/octet-stream")}
        upload_response = client.post("/api/v1/print/cli/upload", files=files)
        file_id = upload_response.json()["file_id"]

        # Test start_layer too low (0)
        layer_range_request = {
            "start_layer": 0,
            "end_layer": 2
        }
        response = client.post(
            f"/api/v1/print/cli/{file_id}/layers/send/1",
            json=layer_range_request
        )
        assert response.status_code == 422  # Pydantic validation error

        # Test end_layer too high (4 > 3 layers)
        layer_range_request = {
            "start_layer": 1,
            "end_layer": 4
        }
        response = client.post(
            f"/api/v1/print/cli/{file_id}/layers/send/1",
            json=layer_range_request
        )
        assert response.status_code == 400
        assert "Invalid layer range. Must be between 1 and 3" in response.json()["detail"]

        # Test start_layer too high
        layer_range_request = {
            "start_layer": 5,
            "end_layer": 5
        }
        response = client.post(
            f"/api/v1/print/cli/{file_id}/layers/send/1",
            json=layer_range_request
        )
        assert response.status_code == 400
        assert "Invalid layer range. Must be between 1 and 3" in response.json()["detail"]

    def test_send_cli_layer_range_invalid_drum_id(self, client):
        """Test validation error for invalid drum ID."""
        layer_range_request = {
            "start_layer": 1,
            "end_layer": 2
        }

        # Test drum ID too high (only 0, 1, 2 are valid)
        response = client.post(
            "/api/v1/print/cli/some-file-id/layers/send/3",
            json=layer_range_request
        )
        assert response.status_code == 422  # Validation error

        # Test negative drum ID
        response = client.post(
            "/api/v1/print/cli/some-file-id/layers/send/-1",
            json=layer_range_request
        )
        assert response.status_code == 422  # Validation error

    @patch('app.api.print.Editor')
    def test_send_cli_layer_range_connection_error(self, mock_parser_class, client, mock_recoater_client):
        """Test layer range send with recoater connection error."""
        # Setup mock parser
        mock_parser = Mock()
        mock_parser_class.return_value = mock_parser
        mock_parsed_data = ParsedCliFile(
            header_lines=["$$HEADEREND"],
            is_aligned=False,
            layers=[CliLayer(z_height=0.1, polylines=[], hatches=[])] * 3
        )
        mock_parser.parse.return_value = mock_parsed_data
        mock_parser.generate_ascii_cli_from_layer_range.return_value = b"cli data"

        # Mock connection error
        mock_recoater_client.upload_drum_geometry.side_effect = RecoaterConnectionError("Connection failed")

        # Upload file
        files = {"file": ("test.cli", BytesIO(b"content"), "application/octet-stream")}
        upload_response = client.post("/api/v1/print/cli/upload", files=files)
        file_id = upload_response.json()["file_id"]

        # Send layer range
        layer_range_request = {
            "start_layer": 1,
            "end_layer": 2
        }
        response = client.post(
            f"/api/v1/print/cli/{file_id}/layers/send/1",
            json=layer_range_request
        )

        assert response.status_code == 503
        assert "Connection error" in response.json()["detail"]

    @patch('app.api.print.Editor')
    def test_send_cli_layer_range_api_error(self, mock_parser_class, client, mock_recoater_client):
        """Test layer range send with recoater API error."""
        # Setup mock parser
        mock_parser = Mock()
        mock_parser_class.return_value = mock_parser
        mock_parsed_data = ParsedCliFile(
            header_lines=["$$HEADEREND"],
            is_aligned=False,
            layers=[CliLayer(z_height=0.1, polylines=[], hatches=[])] * 3
        )
        mock_parser.parse.return_value = mock_parsed_data
        mock_parser.generate_ascii_cli_from_layer_range.return_value = b"cli data"

        # Mock API error
        mock_recoater_client.upload_drum_geometry.side_effect = RecoaterAPIError("Invalid file format")

        # Upload file
        files = {"file": ("test.cli", BytesIO(b"content"), "application/octet-stream")}
        upload_response = client.post("/api/v1/print/cli/upload", files=files)
        file_id = upload_response.json()["file_id"]

        # Send layer range
        layer_range_request = {
            "start_layer": 1,
            "end_layer": 2
        }
        response = client.post(
            f"/api/v1/print/cli/{file_id}/layers/send/1",
            json=layer_range_request
        )

        assert response.status_code == 400
        assert "API error" in response.json()["detail"]

    @patch('app.api.print.Editor')
    def test_send_cli_layer_range_cli_generation_error(self, mock_parser_class, client):
        """Test layer range send with CLI generation error."""
        # Setup mock parser
        mock_parser = Mock()
        mock_parser_class.return_value = mock_parser
        mock_parsed_data = ParsedCliFile(
            header_lines=["$$HEADEREND"],
            is_aligned=False,
            layers=[CliLayer(z_height=0.1, polylines=[], hatches=[])] * 3
        )
        mock_parser.parse.return_value = mock_parsed_data

        # Mock CLI generation error
        mock_parser.generate_ascii_cli_from_layer_range.side_effect = CliParsingError("Failed to generate CLI data")

        # Upload file
        files = {"file": ("test.cli", BytesIO(b"content"), "application/octet-stream")}
        upload_response = client.post("/api/v1/print/cli/upload", files=files)
        file_id = upload_response.json()["file_id"]

        # Send layer range
        layer_range_request = {
            "start_layer": 1,
            "end_layer": 2
        }
        response = client.post(
            f"/api/v1/print/cli/{file_id}/layers/send/1",
            json=layer_range_request
        )

        assert response.status_code == 400
        assert "CLI generation error" in response.json()["detail"]
        assert "Failed to generate CLI data" in response.json()["detail"]

    def test_send_cli_layer_range_invalid_request_body(self, client):
        """Test validation errors for invalid request body."""
        # Missing required fields
        response = client.post(
            "/api/v1/print/cli/some-file-id/layers/send/1",
            json={}
        )
        assert response.status_code == 422  # Validation error

        # Invalid field types
        response = client.post(
            "/api/v1/print/cli/some-file-id/layers/send/1",
            json={
                "start_layer": "not_a_number",
                "end_layer": 2
            }
        )
        assert response.status_code == 422  # Validation error

        # Negative layer numbers
        response = client.post(
            "/api/v1/print/cli/some-file-id/layers/send/1",
            json={
                "start_layer": -1,
                "end_layer": 2
            }
        )
        assert response.status_code == 422  # Validation error


class TestCliParserLayerRange:
    """Test CLI parser service layer range functionality."""

    def test_generate_ascii_cli_from_layer_range_basic(self):
        """Test basic CLI generation from multiple layers."""
        from backend.infrastructure.cli_editor.editor import Editor, CliLayer, Point, Polyline, Hatch

        parser = Editor()

        # Create layers with different content
        layer1 = CliLayer(
            z_height=0.1,
            polylines=[
                Polyline(
                    part_id=1,
                    direction=0,
                    points=[Point(x=0.0, y=0.0), Point(x=5.0, y=5.0)]
                )
            ],
            hatches=[]
        )

        layer2 = CliLayer(
            z_height=0.2,
            polylines=[],
            hatches=[
                Hatch(
                    group_id=1,
                    lines=[(Point(x=1.0, y=1.0), Point(x=4.0, y=4.0))]
                )
            ]
        )

        # Generate CLI data from layer range
        cli_data = parser.generate_ascii_cli_from_layer_range([layer1, layer2])

        # Verify we got bytes back
        assert isinstance(cli_data, bytes)
        assert len(cli_data) > 0

        # Verify header is present
        assert b"$$HEADEREND" in cli_data

    def test_generate_ascii_cli_from_layer_range_with_custom_header(self):
        """Test CLI generation with custom header and alignment."""
        from backend.infrastructure.cli_editor.editor import Editor, CliLayer

        parser = Editor()

        layers = [
            CliLayer(z_height=0.5, polylines=[], hatches=[]),
            CliLayer(z_height=1.0, polylines=[], hatches=[]),
            CliLayer(z_height=1.5, polylines=[], hatches=[])
        ]
        custom_header = ["$$UNITS/1", "$$ALIGN", "$$HEADEREND"]

        cli_data = parser.generate_ascii_cli_from_layer_range(
            layers=layers,
            header_lines=custom_header,
            is_aligned=True
        )

        # Verify custom header is present
        assert b"$$UNITS/1" in cli_data
        assert b"$$HEADEREND" in cli_data

    def test_generate_ascii_cli_from_layer_range_empty_layers(self):
        """Test CLI generation with empty layer list."""
        from backend.infrastructure.cli_editor.editor import Editor
        from backend.infrastructure.cli_editor.cli_exceptions import CliGenerationError

        parser = Editor()

        # Test with empty layer list
        with pytest.raises(CliGenerationError) as exc_info:
            parser.generate_ascii_cli_from_layer_range([])

        assert "Cannot generate CLI data from an empty layer range" in str(exc_info.value)

    def test_generate_ascii_cli_from_layer_range_single_layer(self):
        """Test CLI generation with single layer in range."""
        from backend.infrastructure.cli_editor.editor import Editor, CliLayer, Point, Polyline

        parser = Editor()

        layer = CliLayer(
            z_height=2.5,
            polylines=[
                Polyline(
                    part_id=42,
                    direction=1,
                    points=[Point(x=10.0, y=20.0), Point(x=30.0, y=40.0)]
                )
            ],
            hatches=[]
        )

        cli_data = parser.generate_ascii_cli_from_layer_range([layer])

        assert isinstance(cli_data, bytes)
        assert len(cli_data) > 0
        assert b"$$HEADEREND" in cli_data

    def test_generate_ascii_cli_from_layer_range_complex_geometry(self):
        """Test CLI generation with complex geometry across multiple layers."""
        from backend.infrastructure.cli_editor.editor import Editor, CliLayer, Point, Polyline, Hatch

        parser = Editor()

        # Create layers with complex geometry
        layer1 = CliLayer(
            z_height=0.1,
            polylines=[
                Polyline(
                    part_id=1,
                    direction=0,
                    points=[Point(x=0.0, y=0.0), Point(x=10.0, y=0.0), Point(x=10.0, y=10.0)]
                ),
                Polyline(
                    part_id=2,
                    direction=1,
                    points=[Point(x=5.0, y=5.0), Point(x=15.0, y=5.0)]
                )
            ],
            hatches=[
                Hatch(
                    group_id=1,
                    lines=[
                        (Point(x=1.0, y=1.0), Point(x=9.0, y=1.0)),
                        (Point(x=1.0, y=2.0), Point(x=9.0, y=2.0))
                    ]
                )
            ]
        )

        layer2 = CliLayer(
            z_height=0.2,
            polylines=[
                Polyline(
                    part_id=3,
                    direction=0,
                    points=[Point(x=20.0, y=20.0), Point(x=30.0, y=30.0)]
                )
            ],
            hatches=[
                Hatch(
                    group_id=2,
                    lines=[(Point(x=25.0, y=25.0), Point(x=35.0, y=35.0))]
                ),
                Hatch(
                    group_id=3,
                    lines=[(Point(x=21.0, y=21.0), Point(x=29.0, y=29.0))]
                )
            ]
        )

        cli_data = parser.generate_ascii_cli_from_layer_range([layer1, layer2])

        assert isinstance(cli_data, bytes)
        assert len(cli_data) > 0
        assert b"$$HEADEREND" in cli_data

    def test_generate_ascii_cli_from_layer_range_no_header_provided(self):
        """Test CLI generation when no header is provided."""
        from backend.infrastructure.cli_editor.editor import Editor, CliLayer

        parser = Editor()

        layers = [
            CliLayer(z_height=0.1, polylines=[], hatches=[]),
            CliLayer(z_height=0.2, polylines=[], hatches=[])
        ]

        # Call without providing header_lines (should use default)
        cli_data = parser.generate_ascii_cli_from_layer_range(layers)

        assert isinstance(cli_data, bytes)
        assert len(cli_data) > 0
        assert b"$$HEADEREND" in cli_data

    def test_generate_ascii_cli_from_layer_range_different_z_heights(self):
        """Test CLI generation with layers at different Z heights."""
        from backend.infrastructure.cli_editor.editor import Editor, CliLayer

        parser = Editor()

        layers = [
            CliLayer(z_height=0.05, polylines=[], hatches=[]),
            CliLayer(z_height=0.15, polylines=[], hatches=[]),
            CliLayer(z_height=0.25, polylines=[], hatches=[]),
            CliLayer(z_height=0.35, polylines=[], hatches=[])
        ]

        cli_data = parser.generate_ascii_cli_from_layer_range(layers)

        assert isinstance(cli_data, bytes)
        assert len(cli_data) > 0

    def test_generate_ascii_cli_from_layer_range_aligned_format(self):
        """Test CLI generation with aligned format enabled."""
        from backend.infrastructure.cli_editor.editor import Editor, CliLayer, Point, Polyline

        parser = Editor()

        layer = CliLayer(
            z_height=1.0,
            polylines=[
                Polyline(
                    part_id=1,
                    direction=0,
                    points=[Point(x=0.0, y=0.0), Point(x=1.0, y=1.0)]
                )
            ],
            hatches=[]
        )

        # Test with alignment enabled
        cli_data = parser.generate_ascii_cli_from_layer_range(
            layers=[layer],
            is_aligned=True
        )

        assert isinstance(cli_data, bytes)
        assert len(cli_data) > 0

    def test_generate_ascii_cli_from_layer_range_large_number_of_layers(self):
        """Test CLI generation with a large number of layers."""
        from backend.infrastructure.cli_editor.editor import Editor, CliLayer

        parser = Editor()

        # Create 50 layers
        layers = [
            CliLayer(z_height=i * 0.1, polylines=[], hatches=[])
            for i in range(1, 51)
        ]

        cli_data = parser.generate_ascii_cli_from_layer_range(layers)

        assert isinstance(cli_data, bytes)
        assert len(cli_data) > 0
        assert b"$$HEADEREND" in cli_data
