"""
Multi-Material Job Manager Service
==================================

This service manages multi-material print jobs with 3-drum coordination.
It handles job initialization, layer progression, drum synchronization,
and error management for the multi-material printing workflow.

Based on Stage 2 requirements from the implementation documentation.
"""

import asyncio
import logging
import time
import uuid
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import asdict

from app.models.multilayer_job import (
    MultiMaterialJobState, 
    JobStatus, 
    DrumState, 
    LayerData
)
from backend.infrastructure.cli_editor.editor import Editor, ParsedCliFile, CliParsingError
from infrastructure.recoater_client import RecoaterClient, RecoaterConnectionError, RecoaterAPIError
from app.services.opcua_coordinator import opcua_coordinator

logger = logging.getLogger(__name__)


class MultiMaterialJobError(Exception):
    """Raised when multi-material job operations fail."""
    pass


class MultiMaterialJobManager:
    """
    Manages multi-material print jobs with 3-drum coordination.
    
    This service handles:
    - Job initialization with 3 CLI files validation
    - Multi-drum layer sequence management
    - Progress tracking across all drums
    - Error state management per drum
    - Coordinated layer upload management
    """
    
    def __init__(self, recoater_client: RecoaterClient):
        """
        Initialize the multi-material job manager.
        
        Args:
            recoater_client: Client for communicating with recoater hardware
        """
        self.recoater_client = recoater_client
        self.cli_parser = Editor()
        
        # Active job state
        self.current_job: Optional[MultiMaterialJobState] = None

        # CLI file cache (file_id -> ParsedCliFile)
        self.cli_cache: Dict[str, ParsedCliFile] = {}

        # Coordination engine will be set when needed (Stage 3)
        self._coordination_engine = None

        logger.info("MultiMaterialJobManager initialized")
    
    async def create_job(self, file_ids: Dict[int, str]) -> MultiMaterialJobState:
        """
        Create a new multi-material job with 3 CLI files.
        
        Args:
            file_ids: Dictionary mapping drum_id (0,1,2) -> file_id
            
        Returns:
            MultiMaterialJobState: The created job state
            
        Raises:
            MultiMaterialJobError: If job creation fails
        """
        try:
            # Validate input - now allow 1, 2 or 3 files (single/dual/triple material printing)
            # 1 file => add empty templates for remaining 2 drums
            # 2 files => add empty template for remaining drum
            # 3 files => use as-is
            if not file_ids or len(file_ids) < 1 or len(file_ids) > 3:
                raise MultiMaterialJobError("Must provide 1-3 CLI files for single/dual/triple material printing")

            # Validate provided files exist in cache
            for drum_id, file_id in file_ids.items():
                if drum_id not in [0, 1, 2]:
                    raise MultiMaterialJobError(f"Invalid drum ID: {drum_id}. Must be 0, 1, or 2")
                if file_id not in self.cli_cache:
                    raise MultiMaterialJobError(f"CLI file {file_id} not found in cache")

            # For single/dual material printing, automatically add empty layer template(s) for missing drums
            if len(file_ids) in (1, 2):
                all_drums = {0, 1, 2}
                provided_drums = set(file_ids.keys())
                missing_drums = list(all_drums - provided_drums)
                missing_drums.sort()

                expected_missing_count = 2 if len(file_ids) == 1 else 1
                if len(missing_drums) != expected_missing_count:
                    raise MultiMaterialJobError(
                        "For single material printing two drums should be missing; for dual one drum should be missing"
                    )

                label = "Single" if len(file_ids) == 1 else "Dual"
                logger.info(f"{label} material printing: Adding empty template(s) for drum(s) {missing_drums}")

                # Load an empty template per missing drum (separate IDs to keep mapping explicit)
                for md in missing_drums:
                    empty_template_id = await self._load_empty_layer_template()
                    file_ids[md] = empty_template_id
            
            # Get parsed CLI files and validate layer counts
            parsed_files = {}
            layer_counts = {}
            
            for drum_id, file_id in file_ids.items():
                parsed_file = self.cli_cache[file_id]
                parsed_files[drum_id] = parsed_file
                layer_counts[drum_id] = len(parsed_file.layers)
                logger.info(f"Drum {drum_id}: {layer_counts[drum_id]} layers")
            
            # Calculate total layers as maximum across all drums
            total_layers = max(layer_counts.values())
            logger.info(f"Total layers calculated as maximum: {total_layers}")
            
            # Create job state
            job_state = MultiMaterialJobState(
                file_ids=file_ids.copy(),
                total_layers=total_layers
            )
            
            # Prepare layer data for each drum
            for drum_id, parsed_file in parsed_files.items():
                # Convert CLI layers to LayerData objects
                layer_data_list = []
                for i, cli_layer in enumerate(parsed_file.layers):
                    layer_data = LayerData(
                        layer_number=i + 1,  # 1-based layer numbering
                        cli_data=self._generate_single_layer_cli(parsed_file, i),
                        is_empty=len(cli_layer.polylines) == 0
                    )
                    layer_data_list.append(layer_data)
                
                # Handle depleted drums (pad with empty layers if needed)
                while len(layer_data_list) < total_layers:
                    empty_layer = LayerData(
                        layer_number=len(layer_data_list) + 1,
                        cli_data=b"",  # Empty CLI data for depleted drum
                        is_empty=True
                    )
                    layer_data_list.append(empty_layer)
                
                job_state.remaining_layers[drum_id] = layer_data_list
                job_state.header_lines[drum_id] = parsed_file.header_lines.copy()
                
                # Update drum state
                job_state.drums[drum_id].file_id = file_ids[drum_id]
                job_state.drums[drum_id].total_layers = len(layer_data_list)
                job_state.drums[drum_id].status = "ready"
            
            # Set job as current
            self.current_job = job_state
            logger.info(f"Created multi-material job {job_state.job_id} with {total_layers} layers")
            
            return job_state
            
        except Exception as e:
            logger.error(f"Failed to create multi-material job: {e}")
            raise MultiMaterialJobError(f"Job creation failed: {e}")
    
    async def start_job(self) -> bool:
        """
        Start the active multi-material job.
        
        Returns:
            bool: True if job started successfully
            
        Raises:
            MultiMaterialJobError: If no job is active or start fails
        """
        if not self.current_job:
            raise MultiMaterialJobError("No active job to start")
        
        try:
            # Update job state
            self.current_job.is_active = True
            self.current_job.status = JobStatus.RUNNING
            self.current_job.start_time = time.time()
            self.current_job.current_layer = 1
            
            # Update OPC UA variables
            if opcua_coordinator:
                await opcua_coordinator.set_job_active(self.current_job.total_layers)
                await opcua_coordinator.update_layer_progress(self.current_job.current_layer)
            
            logger.info(f"Started multi-material job {self.current_job.job_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start job: {e}")
            if self.current_job:
                self.current_job.status = JobStatus.ERROR
                self.current_job.error_message = str(e)
            raise MultiMaterialJobError(f"Job start failed: {e}")
    
    async def cancel_job(self) -> bool:
        """
        Cancel the active multi-material job.
        
        Returns:
            bool: True if job cancelled successfully
        """
        if not self.current_job:
            return True  # No job to cancel
        
        try:
            # Update job state
            self.current_job.is_active = False
            self.current_job.status = JobStatus.CANCELLED
            
            # Update OPC UA variables
            if opcua_coordinator:
                await opcua_coordinator.set_job_active(False)
                await opcua_coordinator.clear_error_flags()
            
            logger.info(f"Cancelled multi-material job {self.current_job.job_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to cancel job: {e}")
            return False
    
    def get_job_status(self) -> Optional[Dict[str, Any]]:
        """
        Get the current job status.
        
        Returns:
            Dict containing job status information or None if no active job
        """
        if not self.current_job:
            return None
        
        return {
            "job_id": self.current_job.job_id,
            "is_active": self.current_job.is_active,
            "status": self.current_job.status.value,
            "current_layer": self.current_job.current_layer,
            "total_layers": self.current_job.total_layers,
            "progress_percentage": self.current_job.get_progress_percentage(),
            "error_message": self.current_job.error_message,
            "drums": {
                drum_id: {
                    "status": drum.status,
                    "ready": drum.ready,
                    "uploaded": drum.uploaded,
                    "current_layer": drum.current_layer,
                    "total_layers": drum.total_layers,
                    "error_message": drum.error_message
                }
                for drum_id, drum in self.current_job.drums.items()
            }
        }
    
    def get_drum_status(self, drum_id: int) -> Optional[Dict[str, Any]]:
        """
        Get status for a specific drum.
        
        Args:
            drum_id: The drum ID (0, 1, or 2)
            
        Returns:
            Dict containing drum status or None if no active job
        """
        if not self.current_job or drum_id not in self.current_job.drums:
            return None
        
        drum = self.current_job.drums[drum_id]
        return {
            "drum_id": drum_id,
            "status": drum.status,
            "ready": drum.ready,
            "uploaded": drum.uploaded,
            "current_layer": drum.current_layer,
            "total_layers": drum.total_layers,
            "error_message": drum.error_message,
            "file_id": drum.file_id
        }
    
    def add_cli_file(self, file_id: str, parsed_file: ParsedCliFile) -> None:
        """
        Add a parsed CLI file to the cache.
        
        Args:
            file_id: Unique identifier for the file
            parsed_file: The parsed CLI file data
        """
        self.cli_cache[file_id] = parsed_file
        logger.info(f"Added CLI file {file_id} to cache ({len(parsed_file.layers)} layers)")
    
    def _generate_single_layer_cli(self, parsed_file: ParsedCliFile, layer_index: int) -> bytes:
        """
        Generate CLI data for a single layer.
        
        Args:
            parsed_file: The original parsed CLI file
            layer_index: Zero-based layer index
            
        Returns:
            bytes: ASCII CLI data for the single layer
        """
        if layer_index >= len(parsed_file.layers):
            return b""  # Empty for depleted drums
        
        layer = parsed_file.layers[layer_index]
        
        # Build single-layer CLI content
        lines = []
        
        # Add header lines
        lines.extend(parsed_file.header_lines)
        
        # Add layer data
        lines.append(f"$$LAYER {layer.z_height}")
        
        # Use the CLI parser service to generate proper ASCII CLI format
        return self.cli_parser.generate_single_layer_ascii_cli(layer, parsed_file.header_lines)


    async def upload_layer_to_all_drums(self, layer_num: int) -> bool:
        """
        Upload the specified layer to all 3 drums with coordination delay.

        Args:
            layer_num: 1-based layer number to upload

        Returns:
            bool: True if all uploads successful

        Raises:
            MultiMaterialJobError: If upload fails
        """
        if not self.current_job:
            raise MultiMaterialJobError("No active job")

        if layer_num < 1 or layer_num > self.current_job.total_layers:
            raise MultiMaterialJobError(f"Invalid layer number: {layer_num}")

        try:
            upload_results = {}

            # Upload to each drum with 2-second delay between uploads
            for drum_id in [0, 1, 2]:
                drum = self.current_job.drums[drum_id]

                # Get layer data (0-based index)
                layer_index = layer_num - 1
                if layer_index < len(self.current_job.remaining_layers[drum_id]):
                    layer_data = self.current_job.remaining_layers[drum_id][layer_index]

                    # Skip empty layers for depleted drums
                    if layer_data.is_empty:
                        logger.info(f"Skipping empty layer {layer_num} for depleted drum {drum_id}")
                        drum.uploaded = True
                        upload_results[drum_id] = True
                    else:
                        # Upload layer to drum
                        try:
                            await self._upload_layer_to_drum(drum_id, layer_data.cli_data)
                            drum.uploaded = True
                            drum.current_layer = layer_num
                            upload_results[drum_id] = True
                            logger.info(f"Successfully uploaded layer {layer_num} to drum {drum_id}")
                        except Exception as e:
                            drum.uploaded = False
                            drum.error_message = str(e)
                            upload_results[drum_id] = False
                            logger.error(f"Failed to upload layer {layer_num} to drum {drum_id}: {e}")
                else:
                    # Layer beyond available data - treat as empty
                    drum.uploaded = True
                    upload_results[drum_id] = True

                # 2-second delay between drum uploads to prevent server overload
                if drum_id < 2:  # Don't delay after the last drum
                    await asyncio.sleep(2.0)

            # Check if all uploads were successful
            all_successful = all(upload_results.values())

            if all_successful:
                logger.info(f"Successfully uploaded layer {layer_num} to all drums")
            else:
                failed_drums = [drum_id for drum_id, success in upload_results.items() if not success]
                logger.error(f"Failed to upload layer {layer_num} to drums: {failed_drums}")

            return all_successful

        except Exception as e:
            logger.error(f"Failed to upload layer {layer_num} to drums: {e}")
            raise MultiMaterialJobError(f"Layer upload failed: {e}")

    async def _upload_layer_to_drum(self, drum_id: int, cli_data: bytes) -> None:
        """
        Upload CLI data to a specific drum.

        Args:
            drum_id: The drum ID (0, 1, or 2)
            cli_data: The CLI data to upload (if empty, uses empty CLI template)

        Raises:
            RecoaterConnectionError: If upload fails
        """
        if not cli_data:
            # Empty CLI data - load empty CLI template to prevent layer memory retention
            logger.info(f"Loading empty CLI template for depleted drum {drum_id}")
            try:
                import os
                template_path = os.path.join(os.path.dirname(__file__), "..", "templates", "empty_layer.cli")
                with open(template_path, 'rb') as f:
                    cli_data = f.read()
                logger.info(f"Loaded empty CLI template ({len(cli_data)} bytes) for drum {drum_id}")
            except Exception as e:
                logger.error(f"Failed to load empty CLI template: {e}")
                raise MultiMaterialJobError(f"Failed to load empty CLI template: {e}")

        try:
            # Convert bytes to file-like object for upload
            from io import BytesIO
            cli_file = BytesIO(cli_data)
            cli_file.name = f"layer_drum_{drum_id}.cli"

            # Use recoater client to upload (this would need to be implemented)
            # For now, we'll simulate the upload
            await asyncio.to_thread(
                self.recoater_client._make_request,
                "POST",
                f"/drums/{drum_id}/geometry",
                files={"file": cli_file}
            )

        except Exception as e:
            logger.error(f"Failed to upload CLI data to drum {drum_id}: {e}")
            raise

    async def wait_for_all_drums_ready(self) -> bool:
        """
        Wait for all drums to report ready status.

        Returns:
            bool: True if all drums are ready
        """
        if not self.current_job:
            return False

        max_wait_time = 30.0  # 30 seconds timeout
        check_interval = 1.0  # Check every second
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            try:
                all_ready = True

                # Check each drum status
                for drum_id in [0, 1, 2]:
                    drum_status = await asyncio.to_thread(
                        self.recoater_client.get_state
                    )

                    # Parse drum status (this would depend on actual API response format)
                    # For now, we'll assume drums are ready if no error
                    drum_ready = drum_status.get("state") == "ready"

                    self.current_job.drums[drum_id].ready = drum_ready

                    if not drum_ready:
                        all_ready = False

                if all_ready:
                    logger.info("All drums are ready")
                    return True

                await asyncio.sleep(check_interval)

            except Exception as e:
                logger.error(f"Error checking drum status: {e}")
                await asyncio.sleep(check_interval)

        logger.warning("Timeout waiting for all drums to be ready")
        return False

    async def _load_empty_layer_template(self) -> str:
        """
        Load the empty layer CLI template and add it to the cache.

        Returns:
            str: File ID of the cached empty template

        Raises:
            MultiMaterialJobError: If template loading fails
        """
        try:
            import os
            import uuid

            # Load empty layer template
            template_path = os.path.join(os.path.dirname(__file__), "..", "templates", "empty_layer.cli")

            if not os.path.exists(template_path):
                raise MultiMaterialJobError(f"Empty layer template not found at {template_path}")

            with open(template_path, 'r', encoding='utf-8') as f:
                template_content = f.read()

            # Parse the empty template using CLI parser
            parsed_template = await asyncio.to_thread(
                self.cli_parser.parse,
                template_content.encode('utf-8')
            )

            # Generate unique file ID for the template
            template_file_id = f"empty_template_{uuid.uuid4().hex[:8]}"

            # Cache the parsed template
            self.cli_cache[template_file_id] = parsed_template

            logger.info(f"Loaded empty layer template with ID: {template_file_id}")
            return template_file_id

        except Exception as e:
            logger.error(f"Failed to load empty layer template: {e}")
            raise MultiMaterialJobError(f"Failed to load empty layer template: {e}")

    async def clear_error_flags(self) -> bool:
        """
        Clear error flags for operator error recovery.

        Returns:
            bool: True if errors cleared successfully
        """
        try:
            if self.current_job:
                # Clear job-level errors
                self.current_job.error_message = ""
                self.current_job.retry_count = 0

                # Clear drum-level errors
                for drum in self.current_job.drums.values():
                    drum.error_message = ""

            # Clear OPC UA error flags
            if opcua_coordinator:
                await opcua_coordinator.clear_error_flags()

            logger.info("Cleared all error flags")
            return True

        except Exception as e:
            logger.error(f"Failed to clear error flags: {e}")
            return False

    # Stage 3: Coordination Engine Integration Methods

    def set_coordination_engine(self, coordination_engine):
        """
        Set the coordination engine for automated job execution.

        Args:
            coordination_engine: MultiMaterialCoordinationEngine instance
        """
        self._coordination_engine = coordination_engine
        logger.info("Coordination engine set for automated job execution")

    async def start_coordinated_job(self, job_id: str) -> bool:
        """
        Start a multi-material job with coordination engine automation.

        Args:
            job_id: ID of the job to start

        Returns:
            bool: True if job started successfully

        Raises:
            MultiMaterialJobError: If job start fails
        """
        if not self._coordination_engine:
            raise MultiMaterialJobError("Coordination engine not available")

        if not self.current_job or self.current_job.job_id != job_id:
            raise MultiMaterialJobError(f"Job {job_id} not found or not current")

        try:
            # Start the job with coordination engine
            success = await self._coordination_engine.start_multimaterial_job(self.current_job)

            if success:
                self.current_job.status = JobStatus.ACTIVE
                self.current_job.is_active = True
                logger.info(f"Started coordinated multi-material job {job_id}")

            return success

        except Exception as e:
            logger.error(f"Failed to start coordinated job {job_id}: {e}")
            raise MultiMaterialJobError(f"Failed to start coordinated job: {e}")

    async def stop_coordinated_job(self) -> bool:
        """
        Stop the current coordinated multi-material job.

        Returns:
            bool: True if job stopped successfully
        """
        if not self._coordination_engine:
            logger.warning("No coordination engine available for job stop")
            return False

        try:
            success = await self._coordination_engine.stop_job()

            if success and self.current_job:
                self.current_job.status = JobStatus.CANCELLED
                self.current_job.is_active = False
                logger.info(f"Stopped coordinated job {self.current_job.job_id}")

            return success

        except Exception as e:
            logger.error(f"Failed to stop coordinated job: {e}")
            return False

    def get_coordination_status(self) -> Dict[str, Any]:
        """
        Get coordination engine status.

        Returns:
            dict: Coordination status information
        """
        if not self._coordination_engine:
            return {"available": False, "message": "Coordination engine not available"}

        status = self._coordination_engine.get_coordination_status()
        status["available"] = True
        return status


# Global instance (will be initialized by dependency injection)
multilayer_job_manager: Optional[MultiMaterialJobManager] = None
